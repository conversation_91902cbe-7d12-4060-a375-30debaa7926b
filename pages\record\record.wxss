/* pages/record/record.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.header-animation {
  position: absolute;
  top: 50rpx;
  right: 20rpx;
  width: 120rpx;
  height: 80rpx;
  border-radius: 50%;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

/* 表单样式 */
.required {
  color: #ff4757;
  margin-right: 5rpx;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #4CAF50;
  outline: none;
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-item.active {
  border-width: 3rpx;
  transform: scale(1.05);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 15rpx;
  color: white;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

/* 金额输入 */
.amount-input-wrapper {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  background: #fff;
  overflow: hidden;
}

.currency-symbol {
  padding: 25rpx 20rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 32rpx;
  font-weight: 600;
  border-right: 1rpx solid #ddd;
}

.amount-input {
  flex: 1;
  padding: 25rpx 20rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.amount-input:focus {
  outline: none;
}

.amount-input-wrapper:focus-within {
  border-color: #4CAF50;
}

/* 日期选择器 */
.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  background: #fff;
  cursor: pointer;
}

.date-text {
  font-size: 28rpx;
  color: #333;
}

.date-icon {
  font-size: 32rpx;
}

/* 表单操作按钮 */
.form-actions {
  margin-top: 50rpx;
  display: flex;
  gap: 20rpx;
}

.submit-btn {
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
}

.submit-btn[disabled] {
  background: #ccc !important;
  color: #999 !important;
}

/* 快速金额 */
.quick-amounts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.quick-amount-item {
  padding: 20rpx;
  text-align: center;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-amount-item:active {
  background: #4CAF50;
  color: white;
  transform: scale(0.95);
}

/* 最近记录 */
.recent-bills {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.recent-bill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-bill-item:active {
  background: #e8f5e8;
  transform: scale(0.98);
}

.recent-bill-info {
  flex: 1;
}

.recent-bill-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.recent-bill-category {
  font-size: 24rpx;
  color: #666;
}

.recent-bill-amount {
  font-size: 32rpx;
  color: #4CAF50;
  font-weight: 600;
}

/* 激励文案 */
.motivation-text {
  text-align: center;
  padding: 40rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .quick-amounts {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 动画增强 */
.category-item:active {
  transform: scale(0.95);
}

.form-input:focus,
.form-textarea:focus {
  box-shadow: 0 0 0 3rpx rgba(76, 175, 80, 0.1);
}

/* 加载状态 */
.form-actions .btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 成功提交动画 */
.submit-success {
  animation: submitSuccess 0.6s ease;
}

@keyframes submitSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
    background: #45a049;
  }
  100% {
    transform: scale(1);
  }
}

/* 输入焦点动画 */
.form-input:focus,
.form-textarea:focus {
  animation: inputFocus 0.3s ease;
}

@keyframes inputFocus {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.02);
  }
}

/* 分类选择动画 */
.category-item {
  animation: categoryAppear 0.3s ease;
}

@keyframes categoryAppear {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
