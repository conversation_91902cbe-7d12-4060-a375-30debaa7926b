// pages/record/record.js
const storageManager = require('../../utils/storage.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      categoryId: null,
      amount: '',
      date: '',
      remark: ''
    },
    // 分类列表
    categories: [],
    // 今天日期
    todayDate: '',
    // 是否可以提交
    canSubmit: false,
    // 编辑模式
    editMode: false,
    // 编辑的账单ID
    editBillId: null,
    // 快速金额选项
    quickAmounts: [10, 20, 50, 100, 200, 500],
    // 最近账单
    recentBills: [],
    // 激励文案
    motivationText: '',
    // 显示动画
    showAnimation: true
  },

  onLoad(options) {
    // 初始化页面
    this.initPage()
    
    // 检查是否是编辑模式
    if (options.id) {
      this.loadBillForEdit(options.id)
    }
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadCategories()
    this.loadRecentBills()
    this.setMotivationText()
  },

  // 初始化页面
  initPage() {
    const todayDate = util.getTodayDate()
    this.setData({
      'formData.date': todayDate,
      todayDate: todayDate
    })
    
    // 3秒后隐藏动画
    // setTimeout(() => {
    //   this.setData({ showAnimation: false })
    // }, 3000)
  },

  // 加载分类数据
  loadCategories() {
    const categories = storageManager.getCategories()
    this.setData({ categories })
  },

  // 加载最近账单
  loadRecentBills() {
    const bills = storageManager.getBills()
    const recentBills = bills
      .sort((a, b) => b.createTime - a.createTime)
      .slice(0, 3)
      .map(bill => {
        const category = storageManager.getCategoryById(bill.categoryId)
        return {
          ...bill,
          categoryName: category ? category.name : '未知分类'
        }
      })
    this.setData({ recentBills })
  },

  // 设置激励文案
  setMotivationText() {
    const texts = [
      '来记账啦！💪',
      '每一笔都很重要 ✨',
      '理财从记账开始 📊',
      '坚持记账，财富自由 🎯',
      '今天又记了一笔 👍'
    ]
    const randomText = texts[Math.floor(Math.random() * texts.length)]
    this.setData({ motivationText: randomText })
  },

  // 加载要编辑的账单
  loadBillForEdit(billId) {
    const bill = storageManager.getBillById(billId)
    if (bill) {
      this.setData({
        formData: {
          name: bill.name,
          categoryId: bill.categoryId,
          amount: bill.amount.toString(),
          date: bill.date,
          remark: bill.remark || ''
        },
        editMode: true,
        editBillId: billId
      })
      this.validateForm()
    }
  },

  // 账单名称输入
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value.trim()
    })
    this.validateForm()
  },

  // 分类选择
  onCategorySelect(e) {
    const categoryId = parseInt(e.currentTarget.dataset.id)
    this.setData({
      'formData.categoryId': categoryId
    })
    this.validateForm()
  },

  // 金额输入
  onAmountInput(e) {
    let amount = e.detail.value
    // 只允许数字和小数点
    amount = amount.replace(/[^\d.]/g, '')
    // 只允许一个小数点
    const parts = amount.split('.')
    if (parts.length > 2) {
      amount = parts[0] + '.' + parts.slice(1).join('')
    }
    // 限制小数位数
    if (parts[1] && parts[1].length > 2) {
      amount = parts[0] + '.' + parts[1].substring(0, 2)
    }
    
    this.setData({
      'formData.amount': amount
    })
    this.validateForm()
  },

  // 日期选择
  onDateChange(e) {
    this.setData({
      'formData.date': e.detail.value
    })
    this.validateForm()
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      'formData.remark': e.detail.value
    })
  },

  // 快速金额选择
  onQuickAmountSelect(e) {
    const amount = e.currentTarget.dataset.amount
    this.setData({
      'formData.amount': amount.toString()
    })
    this.validateForm()
  },

  // 最近账单选择
  onRecentBillSelect(e) {
    const bill = e.currentTarget.dataset.bill
    this.setData({
      'formData.name': bill.name,
      'formData.categoryId': bill.categoryId,
      'formData.amount': bill.amount.toString(),
      'formData.remark': bill.remark || ''
    })
    this.validateForm()
  },

  // 表单验证
  validateForm() {
    const { name, categoryId, amount } = this.data.formData
    const canSubmit = name.length > 0 && 
                     categoryId !== null && 
                     util.validateAmount(amount)
    this.setData({ canSubmit })
  },

  // 提交表单
  onSubmit() {
    if (!this.data.canSubmit) {
      util.showError('请完善必填信息')
      return
    }

    const { formData, editMode, editBillId } = this.data
    const billData = {
      name: formData.name,
      categoryId: formData.categoryId,
      amount: parseFloat(formData.amount),
      date: formData.date,
      remark: formData.remark
    }

    util.showLoading('保存中...')

    let result
    if (editMode) {
      result = storageManager.updateBill(editBillId, billData)
    } else {
      result = storageManager.addBill(billData)
    }

    util.hideLoading()

    if (result.success) {
      util.showSuccess(editMode ? '更新成功' : '保存成功')
      
      if (!editMode) {
        // 新增成功后重置表单
        this.resetForm()
      } else {
        // 编辑成功后返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } else {
      util.showError(result.error || '保存失败')
    }
  },

  // 取消编辑
  onCancel() {
    wx.navigateBack()
  },

  // 重置表单
  resetForm() {
    this.setData({
      formData: {
        name: '',
        categoryId: null,
        amount: '',
        date: util.getTodayDate(),
        remark: ''
      },
      canSubmit: false
    })
    this.setMotivationText()
  }
})
