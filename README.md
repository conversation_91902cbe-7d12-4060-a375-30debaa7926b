# 星帐 - 日常记账微信小程序

一个简洁清爽的日常记账微信小程序，帮助用户轻松管理个人财务。

## 功能特性

### 📝 记账页面
- **便捷记账**：快速填写账单名称、选择分类、输入金额
- **分类管理**：6大预设分类（一日三餐、零食饮料、出行交通、生活购物、爱好娱乐、其他）
- **日期选择**：支持选择任意日期，默认今天
- **备注功能**：可添加详细备注信息
- **快速金额**：预设常用金额快速选择
- **最近记录**：显示最近的记账记录，支持快速复用

### 📊 统计页面
- **时间筛选**：支持全部/最近7天/最近30天/特定月份
- **统计概览**：总支出、平均每日支出、最高日支出、记录笔数
- **可视化图表**：
  - 按日期的柱状图和折线图
  - 按分类的饼图展示
- **智能分析**：提供消费习惯分析和建议

### 📋 账单列表页面
- **搜索功能**：支持按账单名称和备注搜索
- **筛选功能**：按日期范围和分类筛选
- **排序功能**：支持按日期和金额排序
- **详情查看**：完整的账单详情弹窗
- **编辑删除**：支持编辑和删除账单记录

### 💾 备份功能
- **数据导出**：支持JSON和TXT格式导出
- **备份范围**：全部数据/按月备份/按周备份
- **数据导入**：支持从JSON文件恢复数据
- **使用说明**：详细的备份使用指南

## 技术特点

### 🎨 设计理念
- **简洁清爽**：采用现代化的卡片式设计
- **色彩搭配**：清新的渐变色彩，视觉舒适
- **动画效果**：丰富的微交互动画，提升用户体验
- **响应式设计**：适配不同屏幕尺寸

### 💾 数据存储
- **本地存储**：使用微信小程序本地存储API
- **数据结构**：合理的数据结构设计，支持高效查询
- **数据安全**：本地存储，数据完全由用户控制

### 🔧 技术架构
- **模块化设计**：清晰的代码结构，易于维护
- **工具函数**：丰富的工具函数库，提高开发效率
- **错误处理**：完善的错误处理机制
- **性能优化**：防抖、节流等性能优化措施

## 项目结构

```
星帐/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── utils/                # 工具函数目录
│   ├── storage.js        # 数据存储管理
│   └── util.js           # 通用工具函数
├── components/           # 组件目录
│   └── common.wxss       # 通用组件样式
├── images/               # 图片资源目录
│   └── README.md         # 图片说明文档
└── pages/                # 页面目录
    ├── record/           # 记账页面
    │   ├── record.js
    │   ├── record.wxml
    │   └── record.wxss
    ├── statistics/       # 统计页面
    │   ├── statistics.js
    │   ├── statistics.wxml
    │   └── statistics.wxss
    ├── list/             # 账单列表页面
    │   ├── list.js
    │   ├── list.wxml
    │   └── list.wxss
    └── backup/           # 备份页面
        ├── backup.js
        ├── backup.wxml
        └── backup.wxss
```

## 使用说明

### 首次使用
1. 打开小程序，自动进入记账页面
2. 填写第一笔账单记录
3. 查看统计页面了解消费情况
4. 在账单列表页面管理所有记录

### 日常使用
1. **记账**：在记账页面快速记录每笔支出
2. **查看**：在统计页面分析消费趋势
3. **管理**：在账单列表页面搜索、编辑、删除记录
4. **备份**：定期在备份页面导出数据

### 数据备份
1. 进入账单列表页面，点击浮动的"备份"按钮
2. 选择备份范围和文件格式
3. 点击"开始备份"导出数据
4. 数据文件保存在微信文件管理中

## 开发说明

### 环境要求
- 微信开发者工具
- 微信小程序基础库 2.0+

### 本地开发
1. 使用微信开发者工具打开项目
2. 配置小程序AppID
3. 编译运行

### 图片资源
请参考 `images/README.md` 添加所需的图片资源：
- 导航栏图标（6个）
- 动画图片（2个）
- 空状态图片（3个）

### 自定义配置
- 修改 `app.js` 中的分类配置
- 调整 `app.wxss` 中的主题色彩
- 在各页面样式文件中自定义UI效果

## 功能扩展

### 可扩展功能
- [ ] 收入记录功能
- [ ] 预算管理功能
- [ ] 多账户支持
- [ ] 云端同步功能
- [ ] 数据分析报告
- [ ] 消费提醒功能

### 技术优化
- [ ] 数据库存储优化
- [ ] 图表库集成
- [ ] 性能监控
- [ ] 错误上报

## 许可证

本项目仅供学习和个人使用。

## 联系方式

如有问题或建议，欢迎反馈。

---

**星帐** - 让记账变得简单有趣 ✨
