// pages/statistics/statistics.js
const storageManager = require('../../utils/storage.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 时间筛选选项
    timeFilters: [
      { label: '全部', value: 'all' },
      { label: '今天', value: 'today' },
      { label: '最近7天', value: '7days' },
      { label: '最近30天', value: '30days' },
      { label: '特定月份', value: 'month' }
    ],
    // 当前筛选
    currentFilter: 'all',
    // 选中的月份
    selectedMonth: '',
    // 时间段文本
    periodText: '',
    // 统计数据
    statistics: {
      totalAmount: '0.00',
      avgDaily: '0.00',
      maxDaily: '0.00',
      totalCount: 0
    },
    // 图表类型
    chartType: 'daily',
    // 日支出数据
    dailyData: [],
    // 分类数据
    categoryData: [],
    // 详细数据
    detailData: [],
    // 分析建议
    suggestions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
  },

  // 初始化页面
  initPage() {
    const currentMonth = new Date().toISOString().slice(0, 7)
    this.setData({
      selectedMonth: currentMonth
    })
  },

  // 加载数据
  loadData() {
    const { currentFilter, selectedMonth } = this.data
    let bills = []
    let startDate = ''
    let endDate = ''
    let periodText = ''

    const today = util.getTodayDate()

    switch (currentFilter) {
      case 'all':
        bills = storageManager.getBills()
        periodText = '全部时间'
        break
      case 'today':
        startDate = today
        endDate = today
        bills = storageManager.getBillsByDateRange(startDate, endDate)
        periodText = '今天'
        break
      case '7days':
        startDate = util.getDateBefore(6)
        endDate = today
        bills = storageManager.getBillsByDateRange(startDate, endDate)
        periodText = '最近7天'
        break
      case '30days':
        startDate = util.getDateBefore(29)
        endDate = today
        bills = storageManager.getBillsByDateRange(startDate, endDate)
        periodText = '最近30天'
        break
      case 'month':
        const year = parseInt(selectedMonth.split('-')[0])
        const month = parseInt(selectedMonth.split('-')[1])
        startDate = util.getMonthFirstDay(year, month)
        endDate = util.getMonthLastDay(year, month)
        bills = storageManager.getBillsByDateRange(startDate, endDate)
        periodText = `${year}年${month}月`
        break
    }

    this.setData({ periodText })
    this.calculateStatistics(bills, startDate, endDate)
    this.generateChartData(bills, startDate, endDate)
    this.generateDetailData(bills)
    this.generateSuggestions(bills)
  },

  // 计算统计数据
  calculateStatistics(bills, startDate, endDate) {
    if (bills.length === 0) {
      this.setData({
        statistics: {
          totalAmount: '0.00',
          avgDaily: '0.00',
          maxDaily: '0.00',
          monthAmount: '0.00'
        }
      })
      return
    }

    // 总支出
    const totalAmount = bills.reduce((sum, bill) => sum + bill.amount, 0)

    // 按日期分组计算
    const dailyAmounts = {}
    bills.forEach(bill => {
      if (!dailyAmounts[bill.date]) {
        dailyAmounts[bill.date] = 0
      }
      dailyAmounts[bill.date] += bill.amount
    })

    // 计算有记录的天数
    const recordDays = Object.keys(dailyAmounts).length

    // 平均每日支出
    const avgDaily = recordDays > 0 ? totalAmount / recordDays : 0

    // 最高日支出
    const maxDaily = Math.max(...Object.values(dailyAmounts))

    // 计算本月支出
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1
    const monthStartDate = util.getMonthFirstDay(currentYear, currentMonth)
    const monthEndDate = util.getMonthLastDay(currentYear, currentMonth)
    const monthBills = storageManager.getBillsByDateRange(monthStartDate, monthEndDate)
    const monthAmount = monthBills.reduce((sum, bill) => sum + bill.amount, 0)

    this.setData({
      statistics: {
        totalAmount: util.formatAmount(totalAmount),
        avgDaily: util.formatAmount(avgDaily),
        maxDaily: util.formatAmount(maxDaily),
        monthAmount: util.formatAmount(monthAmount)
      }
    })
  },

  // 生成图表数据
  generateChartData(bills, startDate, endDate) {
    this.generateDailyData(bills, startDate, endDate)
    this.generateCategoryData(bills)
  },

  // 生成日支出数据
  generateDailyData(bills, startDate, endDate) {
    if (bills.length === 0) {
      this.setData({ dailyData: [] })
      return
    }

    // 按日期分组
    const dailyAmounts = {}
    bills.forEach(bill => {
      if (!dailyAmounts[bill.date]) {
        dailyAmounts[bill.date] = 0
      }
      dailyAmounts[bill.date] += bill.amount
    })

    // 生成日期范围
    let dates = []
    if (startDate && endDate) {
      dates = util.getDateRange(startDate, endDate)
    } else {
      dates = Object.keys(dailyAmounts).sort()
    }

    // 限制显示最近30天的数据
    if (dates.length > 30) {
      dates = dates.slice(-30)
    }

    // 计算最大值用于归一化
    const maxAmount = Math.max(...dates.map(date => dailyAmounts[date] || 0))

    const dailyData = dates.map(date => {
      const amount = dailyAmounts[date] || 0
      return {
        date,
        amount: util.formatAmount(amount),
        height: maxAmount > 0 ? (amount / maxAmount) * 100 : 0,
        label: date.slice(-2) // 只显示日期
      }
    })

    this.setData({ dailyData })
  },

  // 生成分类数据
  generateCategoryData(bills) {
    if (bills.length === 0) {
      this.setData({ categoryData: [] })
      return
    }

    const categories = storageManager.getCategories()
    const categoryAmounts = {}
    
    bills.forEach(bill => {
      if (!categoryAmounts[bill.categoryId]) {
        categoryAmounts[bill.categoryId] = 0
      }
      categoryAmounts[bill.categoryId] += bill.amount
    })

    const totalAmount = bills.reduce((sum, bill) => sum + bill.amount, 0)
    
    const categoryData = categories
      .filter(cat => categoryAmounts[cat.id] > 0)
      .map(cat => {
        const amount = categoryAmounts[cat.id]
        const percentage = ((amount / totalAmount) * 100).toFixed(1)
        return {
          id: cat.id,
          name: cat.name,
          amount: util.formatAmount(amount),
          percentage,
          color: cat.color
        }
      })
      .sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount))

    this.setData({ categoryData })
    
    // 绘制饼图
    this.drawPieChart(categoryData)
  },

  // 绘制饼图
  drawPieChart(data) {
    if (data.length === 0) return

    const ctx = wx.createCanvasContext('pieChart', this)
    const centerX = 150
    const centerY = 150
    const radius = 120
    
    let currentAngle = -Math.PI / 2 // 从顶部开始
    
    data.forEach(item => {
      const percentage = parseFloat(item.percentage)
      const angle = (percentage / 100) * 2 * Math.PI
      
      // 绘制扇形
      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + angle)
      ctx.closePath()
      ctx.setFillStyle(item.color)
      ctx.fill()
      
      currentAngle += angle
    })
    
    ctx.draw()
  },

  // 生成详细数据
  generateDetailData(bills) {
    if (bills.length === 0) {
      this.setData({ detailData: [] })
      return
    }

    const dailyData = {}
    bills.forEach(bill => {
      if (!dailyData[bill.date]) {
        dailyData[bill.date] = { amount: 0, count: 0 }
      }
      dailyData[bill.date].amount += bill.amount
      dailyData[bill.date].count += 1
    })

    const detailData = Object.keys(dailyData)
      .sort((a, b) => b.localeCompare(a))
      .slice(0, 10) // 只显示最近10天
      .map(date => ({
        date,
        amount: util.formatAmount(dailyData[date].amount),
        count: dailyData[date].count
      }))

    this.setData({ detailData })
  },

  // 生成分析建议
  generateSuggestions(bills) {
    const suggestions = []
    
    if (bills.length === 0) {
      suggestions.push('还没有记录，快去记账吧！')
    } else {
      const totalAmount = bills.reduce((sum, bill) => sum + bill.amount, 0)
      const avgAmount = totalAmount / bills.length
      
      if (avgAmount > 100) {
        suggestions.push('单笔支出较高，建议关注大额消费')
      }
      
      // 分析分类占比
      const categoryAmounts = {}
      bills.forEach(bill => {
        if (!categoryAmounts[bill.categoryId]) {
          categoryAmounts[bill.categoryId] = 0
        }
        categoryAmounts[bill.categoryId] += bill.amount
      })
      
      const maxCategoryAmount = Math.max(...Object.values(categoryAmounts))
      const maxCategoryId = Object.keys(categoryAmounts).find(
        id => categoryAmounts[id] === maxCategoryAmount
      )
      
      if (maxCategoryId) {
        const category = storageManager.getCategoryById(parseInt(maxCategoryId))
        if (category) {
          const percentage = ((maxCategoryAmount / totalAmount) * 100).toFixed(1)
          suggestions.push(`${category.name}支出占比最高(${percentage}%)`)
        }
      }
      
      if (bills.length >= 7) {
        suggestions.push('记账习惯很好，继续保持！')
      }
    }
    
    this.setData({ suggestions })
  },

  // 时间筛选切换
  onTimeFilterChange(e) {
    const value = e.currentTarget.dataset.value
    this.setData({ currentFilter: value })
    this.loadData()
  },

  // 月份选择
  onMonthChange(e) {
    this.setData({ selectedMonth: e.detail.value })
    this.loadData()
  },

  // 图表类型切换
  onChartTypeChange(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ chartType: type })
  },

  // 画布触摸事件
  onCanvasTouch(e) {
    // 可以添加饼图交互逻辑
    console.log('Canvas touched', e)
  }
})
