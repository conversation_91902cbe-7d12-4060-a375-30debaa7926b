/* pages/backup/backup.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #333;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 数据统计 */
.data-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  border: 1rpx solid #e0e0e0;
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 15rpx;
}

.stat-info {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 备份选项 */
.backup-options {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 单选组 */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.radio-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.radio-item.active {
  border-color: #a8edea;
  background: linear-gradient(135deg, #f0fdfc, #e6fffa);
}

.radio-icon {
  font-size: 32rpx;
  color: #a8edea;
  margin-right: 15rpx;
  margin-top: 5rpx;
}

.radio-text {
  flex: 1;
}

.radio-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.radio-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 月份选择器 */
.month-selector {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-top: 15rpx;
}

.selector-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.month-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: white;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  cursor: pointer;
  min-width: 200rpx;
}

.picker-icon {
  font-size: 28rpx;
  color: #666;
}

/* 格式选择 */
.format-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 6rpx;
  gap: 6rpx;
}

.format-tab {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-tab.active {
  background: #a8edea;
  color: white;
  font-weight: 500;
}

.format-desc {
  margin-top: 15rpx;
  padding: 15rpx;
  background: #fff3cd;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 备份按钮 */
.backup-actions {
  margin-top: 30rpx;
}

.backup-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.backup-btn[disabled] {
  background: #ccc !important;
  color: #999 !important;
}

/* 导入区域 */
.import-section {
  text-align: center;
  padding: 20rpx;
}

.import-desc {
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.import-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.import-warning {
  padding: 15rpx;
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 备份历史 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.history-info {
  flex: 1;
}

.history-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.history-meta {
  font-size: 24rpx;
  color: #666;
}

.history-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  background: #a8edea;
  color: white;
  border-radius: 15rpx;
  font-size: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 使用说明 */
.tips-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
  border-radius: 12rpx;
  border-left: 4rpx solid #4CAF50;
}

.tip-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #2e7d32;
  line-height: 1.5;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .data-stats {
    grid-template-columns: 1fr;
  }
  
  .month-selector {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .history-actions {
    align-self: flex-end;
  }
}

/* 动画效果 */
.radio-item:active {
  transform: scale(0.98);
}

.format-tab:active {
  transform: scale(0.95);
}

.stat-item {
  transition: transform 0.3s ease;
}

.stat-item:active {
  transform: scale(0.98);
}
