<!--pages/record/record.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">记一笔</view>
    <view class="header-subtitle">记录每一笔支出 💰</view>
    <image class="header-animation" src="/images/record-animation.gif" wx:if="{{showAnimation}}"></image>
  </view>

  <!-- 记账表单 -->
  <view class="card fade-in">
    <view class="card-body">
      <!-- 账单名称 -->
      <view class="form-group">
        <view class="form-label">
          <text class="required">*</text>账单名称
        </view>
        <input
          class="form-input"
          placeholder="请输入账单名称"
          value="{{formData.name}}"
          bindinput="onNameInput"
          maxlength="20"
        />
      </view>

      <!-- 分类选择 -->
      <view class="form-group">
        <view class="form-label">
          <text class="required">*</text>选择分类
        </view>
        <view class="category-grid">
          <view
            wx:for="{{categories}}"
            wx:key="id"
            class="category-item {{formData.categoryId === item.id ? 'active' : ''}}"
            style="border-color: {{item.color}}"
            bindtap="onCategorySelect"
            data-id="{{item.id}}"
          >
            <view class="category-icon" style="background-color: {{item.color}}">
              {{item.icon}}
            </view>
            <view class="category-name">{{item.name}}</view>
          </view>
        </view>
      </view>

      <!-- 金额输入 -->
      <view class="form-group">
        <view class="form-label">
          <text class="required">*</text>金额
        </view>
        <view class="amount-input-wrapper">
          <text class="currency-symbol">¥</text>
          <input
            class="amount-input"
            type="digit"
            placeholder="0.00"
            value="{{formData.amount}}"
            bindinput="onAmountInput"
          />
        </view>
      </view>

      <!-- 日期选择 -->
      <view class="form-group">
        <view class="form-label">日期</view>
        <picker
          mode="date"
          value="{{formData.date}}"
          bindchange="onDateChange"
          end="{{todayDate}}"
        >
          <view class="date-picker">
            <text class="date-text">{{formData.date}}</text>
            <text class="date-icon">📅</text>
          </view>
        </picker>
      </view>

      <!-- 备注 -->
      <view class="form-group">
        <view class="form-label">备注</view>
        <textarea
          class="form-textarea"
          placeholder="添加备注信息（可选）"
          value="{{formData.remark}}"
          bindinput="onRemarkInput"
          maxlength="100"
          auto-height
        />
      </view>

      <!-- 提交按钮 -->
      <view class="form-actions">
        <button
          class="btn btn-primary submit-btn"
          bindtap="onSubmit"
          disabled="{{!canSubmit}}"
        >
          {{editMode ? '更新账单' : '保存账单'}}
        </button>
        <button
          wx:if="{{editMode}}"
          class="btn btn-secondary cancel-btn"
          bindtap="onCancel"
        >
          取消
        </button>
      </view>
    </view>
  </view>

  <!-- 快速金额选择 -->
  <view class="card fade-in" style="animation-delay: 0.1s">
    <view class="card-header">
      <view class="card-title">快速金额</view>
    </view>
    <view class="card-body">
      <view class="quick-amounts">
        <view 
          wx:for="{{quickAmounts}}" 
          wx:key="*this"
          class="quick-amount-item"
          bindtap="onQuickAmountSelect"
          data-amount="{{item}}"
        >
          ¥{{item}}
        </view>
      </view>
    </view>
  </view>

  <!-- 最近记录 -->
  <view class="card fade-in" style="animation-delay: 0.2s" wx:if="{{recentBills.length > 0}}">
    <view class="card-header">
      <view class="card-title">最近记录</view>
    </view>
    <view class="card-body">
      <view class="recent-bills">
        <view 
          wx:for="{{recentBills}}" 
          wx:key="id"
          class="recent-bill-item"
          bindtap="onRecentBillSelect"
          data-bill="{{item}}"
        >
          <view class="recent-bill-info">
            <view class="recent-bill-name">{{item.name}}</view>
            <view class="recent-bill-category">{{item.categoryName}}</view>
          </view>
          <view class="recent-bill-amount">¥{{item.amount}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 激励文案 -->
  <view class="motivation-text fade-in" style="animation-delay: 0.3s">
    <text>{{motivationText}}</text>
  </view>
</view>
