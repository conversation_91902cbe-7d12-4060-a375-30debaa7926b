{"appid": "wx13cd44828bbcc1a9", "compileType": "miniprogram", "libVersion": "3.9.1", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "uglifyFileName": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "projectArchitecture": "miniProgram"}