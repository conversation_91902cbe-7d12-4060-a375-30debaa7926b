// pages/list/list.js
const storageManager = require('../../utils/storage.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 原始账单数据
    bills: [],
    // 筛选后的账单数据
    filteredBills: [],
    // 搜索关键词
    searchKeyword: '',
    // 筛选条件
    filterStartDate: '',
    filterEndDate: '',
    selectedCategoryIndex: -1,
    categoryOptions: [],
    // 排序方式
    sortBy: 'date', // date, amount
    sortOrder: 'desc', // asc, desc
    // 详情弹窗
    showDetailModal: false,
    selectedBill: {}
  },

  onLoad() {
    this.loadCategories()
  },

  onShow() {
    this.loadBills()
  },

  // 加载分类数据
  loadCategories() {
    const categories = storageManager.getCategories()
    const categoryOptions = [{ id: -1, name: '全部分类' }, ...categories]
    this.setData({ categoryOptions })
  },

  // 加载账单数据
  loadBills() {
    const bills = storageManager.getBills()
    const processedBills = bills.map(bill => {
      const category = storageManager.getCategoryById(bill.categoryId)
      return {
        ...bill,
        categoryName: category ? category.name : '未知分类',
        categoryIcon: category ? category.icon : '📦',
        categoryColor: category ? category.color : '#DDA0DD',
        createTimeText: new Date(bill.createTime).toLocaleString()
      }
    })

    this.setData({ bills: processedBills })
    this.applyFiltersAndSort()
  },

  // 应用筛选和排序
  applyFiltersAndSort() {
    let { bills, searchKeyword, filterStartDate, filterEndDate, selectedCategoryIndex, sortBy, sortOrder } = this.data
    let filteredBills = [...bills]

    // 搜索筛选
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase()
      filteredBills = filteredBills.filter(bill =>
        bill.name.toLowerCase().includes(keyword) ||
        (bill.remark && bill.remark.toLowerCase().includes(keyword))
      )
    }

    // 日期筛选
    if (filterStartDate) {
      filteredBills = filteredBills.filter(bill => bill.date >= filterStartDate)
    }
    if (filterEndDate) {
      filteredBills = filteredBills.filter(bill => bill.date <= filterEndDate)
    }

    // 分类筛选
    if (selectedCategoryIndex >= 0) {
      const selectedCategory = this.data.categoryOptions[selectedCategoryIndex]
      if (selectedCategory && selectedCategory.id !== -1) {
        filteredBills = filteredBills.filter(bill => bill.categoryId === selectedCategory.id)
      }
    }

    // 排序
    filteredBills.sort((a, b) => {
      let compareValue = 0
      if (sortBy === 'date') {
        compareValue = a.date.localeCompare(b.date)
      } else if (sortBy === 'amount') {
        compareValue = a.amount - b.amount
      }
      return sortOrder === 'desc' ? -compareValue : compareValue
    })

    this.setData({ filteredBills })
  },

  // 搜索输入
  onSearchInput: util.debounce(function(e) {
    this.setData({ searchKeyword: e.detail.value })
    this.applyFiltersAndSort()
  }, 300),

  // 搜索确认
  onSearch() {
    this.applyFiltersAndSort()
  },

  // 开始日期选择
  onStartDateChange(e) {
    this.setData({ filterStartDate: e.detail.value })
  },

  // 结束日期选择
  onEndDateChange(e) {
    this.setData({ filterEndDate: e.detail.value })
  },

  // 分类选择
  onCategoryChange(e) {
    this.setData({ selectedCategoryIndex: parseInt(e.detail.value) })
  },

  // 清除筛选
  onClearFilter() {
    this.setData({
      searchKeyword: '',
      filterStartDate: '',
      filterEndDate: '',
      selectedCategoryIndex: -1
    })
    this.applyFiltersAndSort()
  },

  // 应用筛选
  onApplyFilter() {
    this.applyFiltersAndSort()
  },

  // 排序切换
  onSortChange(e) {
    const newSortBy = e.currentTarget.dataset.sort
    let newSortOrder = 'desc'

    // 如果点击的是当前排序字段，则切换排序顺序
    if (newSortBy === this.data.sortBy) {
      newSortOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    }

    this.setData({
      sortBy: newSortBy,
      sortOrder: newSortOrder
    })
    this.applyFiltersAndSort()
  },

  // 账单详情
  onBillDetail(e) {
    e.stopPropagation()
    const bill = e.currentTarget.dataset.bill
    this.setData({
      selectedBill: bill,
      showDetailModal: true
    })
  },

  // 关闭详情弹窗
  closeDetailModal() {
    this.setData({ showDetailModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 从详情编辑
  editFromDetail() {
    const billId = this.data.selectedBill.id
    this.closeDetailModal()
    wx.navigateTo({
      url: `/pages/record/record?id=${billId}`
    })
  },

  // 编辑账单
  onBillEdit(e) {
    e.stopPropagation()
    const billId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/record/record?id=${billId}`
    })
  },

  // 删除账单
  async onBillDelete(e) {
    e.stopPropagation()
    const billId = e.currentTarget.dataset.id

    const confirmed = await util.showConfirm('确定要删除这条账单记录吗？', '删除确认')
    if (!confirmed) return

    util.showLoading('删除中...')
    const result = storageManager.deleteBill(billId)
    util.hideLoading()

    if (result.success) {
      util.showSuccess('删除成功')
      this.loadBills()
    } else {
      util.showError(result.error || '删除失败')
    }
  },

  // 去记账
  goToRecord() {
    wx.switchTab({
      url: '/pages/record/record'
    })
  },

  // 去备份
  goToBackup() {
    wx.navigateTo({
      url: '/pages/backup/backup'
    })
  }
})