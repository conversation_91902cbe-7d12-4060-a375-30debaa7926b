<!--pages/backup/backup.wxml-->
<view class="container">
  <!-- ҳ����� -->
  <view class="page-header">
    <view class="header-title">���ݱ���</view>
    <view class="header-subtitle">������ļ������� ?</view>
  </view>

  <!-- ���ݸ��� -->
  <view class="card fade-in">
    <view class="card-header">
      <view class="card-title">���ݸ���</view>
    </view>
    <view class="card-body">
      <view class="data-stats">
        <view class="stat-item">
          <view class="stat-icon">?</view>
          <view class="stat-info">
            <view class="stat-value">{{totalBills}}</view>
            <view class="stat-label">�˵���¼</view>
          </view>
        </view>
        <view class="stat-item">
          <view class="stat-icon">?</view>
          <view class="stat-info">
            <view class="stat-value">?{{totalAmount}}</view>
            <view class="stat-label">��֧��</view>
          </view>
        </view>
        <view class="stat-item">
          <view class="stat-icon">?</view>
          <view class="stat-info">
            <view class="stat-value">{{dayCount}}</view>
            <view class="stat-label">��¼����</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- ����ѡ�� -->
  <view class="card fade-in" style="animation-delay: 0.1s">
    <view class="card-header">
      <view class="card-title">����ѡ��</view>
    </view>
    <view class="card-body">
      <!-- ���ݷ�Χѡ�� -->
      <view class="backup-options">
        <view class="option-group">
          <view class="option-title">���ݷ�Χ</view>
          <view class="radio-group">
            <view
              class="radio-item {{backupRange === 'all' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="all"
            >
              <view class="radio-icon">{{backupRange === 'all' ? '��' : '��'}}</view>
              <view class="radio-text">
                <view class="radio-label">������������</view>
                <view class="radio-desc">���������˵���¼�ͷ�����Ϣ</view>
              </view>
            </view>
            <view
              class="radio-item {{backupRange === 'month' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="month"
            >
              <view class="radio-icon">{{backupRange === 'month' ? '��' : '��'}}</view>
              <view class="radio-text">
                <view class="radio-label">���±���</view>
                <view class="radio-desc">����ָ���·ݵ�����</view>
              </view>
            </view>
            <view
              class="radio-item {{backupRange === 'week' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="week"
            >
              <view class="radio-icon">{{backupRange === 'week' ? '��' : '��'}}</view>
              <view class="radio-text">
                <view class="radio-label">���ܱ���</view>
                <view class="radio-desc">�������һ�ܵ�����</view>
              </view>
            </view>
          </view>
        </view>

        <!-- �·�ѡ�� -->
        <view wx:if="{{backupRange === 'month'}}" class="month-selector">
          <view class="selector-label">ѡ���·�:</view>
          <picker
            mode="date"
            fields="month"
            value="{{selectedMonth}}"
            bindchange="onMonthChange"
          >
            <view class="month-picker">
              <text>{{selectedMonth}}</text>
              <text class="picker-icon">?</text>
            </view>
          </picker>
        </view>

        <!-- �ļ���ʽѡ�� -->
        <view class="option-group">
          <view class="option-title">�ļ���ʽ</view>
          <view class="format-tabs">
            <view
              class="format-tab {{exportFormat === 'json' ? 'active' : ''}}"
              bindtap="onFormatChange"
              data-format="json"
            >
              JSON
            </view>
            <view
              class="format-tab {{exportFormat === 'txt' ? 'active' : ''}}"
              bindtap="onFormatChange"
              data-format="txt"
            >
              TXT
            </view>
          </view>
          <view class="format-desc">
            <text wx:if="{{exportFormat === 'json'}}">JSON��ʽ�������ݵ���ͳ�����</text>
            <text wx:if="{{exportFormat === 'txt'}}">TXT��ʽ�����˹��Ķ��ʹ�ӡ</text>
          </view>
        </view>
      </view>

      <!-- ���ݰ�ť -->
      <view class="backup-actions">
        <button
          class="btn btn-primary backup-btn"
          bindtap="onExportData"
          disabled="{{isExporting}}"
        >
          {{isExporting ? '������...' : '��ʼ����'}}
        </button>
      </view>
    </view>
  </view>

  <!-- �������� -->
  <view class="card fade-in" style="animation-delay: 0.2s">
    <view class="card-header">
      <view class="card-title">��������</view>
    </view>
    <view class="card-body">
      <view class="import-section">
        <view class="import-desc">
          <text>ѡ��֮ǰ���ݵ�JSON�ļ����ָ�����</text>
        </view>
        <button
          class="btn btn-secondary import-btn"
          bindtap="onImportData"
          disabled="{{isImporting}}"
        >
          {{isImporting ? '������...' : 'ѡ���ļ�����'}}
        </button>
        <view class="import-warning">
          <text>?? �������ݽ������������ݣ����������</text>
        </view>
      </view>
    </view>
  </view>

  <!-- ʹ��˵�� -->
  <view class="card fade-in" style="animation-delay: 0.3s">
    <view class="card-header">
      <view class="card-title">? ʹ��˵��</view>
    </view>
    <view class="card-body">
      <view class="tips-list">
        <view class="tip-item">
          <text class="tip-number">1.</text>
          <text class="tip-text">���ڱ������ݣ��������ݶ�ʧ</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">2.</text>
          <text class="tip-text">JSON��ʽ���������ݵ��룬TXT��ʽ���ڲ鿴</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">3.</text>
          <text class="tip-text">��������ǰ�����ȱ��ݵ�ǰ����</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">4.</text>
          <text class="tip-text">�����ļ�������΢���ļ�������</text>
        </view>
      </view>
    </view>
  </view>
</view>