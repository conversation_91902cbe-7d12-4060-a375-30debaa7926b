<!--pages/backup/backup.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">数据备份</view>
    <view class="header-subtitle">保护你的记账数据 💾</view>
    <image class="header-animation" src="/images/22.gif" ></image>
  </view>

  <!-- 数据概览 -->
  <view class="card fade-in">
    <view class="card-header">
      <view class="card-title">数据概览</view>
    </view>
    <view class="card-body">
      <view class="data-stats">
        <view class="stat-item">
          <view class="stat-icon">📝</view>
          <view class="stat-info">
            <view class="stat-value">{{totalBills}}</view>
            <view class="stat-label">账单记录</view>
          </view>
        </view>
        <view class="stat-item">
          <view class="stat-icon">💰</view>
          <view class="stat-info">
            <view class="stat-value">¥{{totalAmount}}</view>
            <view class="stat-label">总支出</view>
          </view>
        </view>
        <view class="stat-item">
          <view class="stat-icon">📅</view>
          <view class="stat-info">
            <view class="stat-value">{{dayCount}}</view>
            <view class="stat-label">记录天数</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 备份选项 -->
  <view class="card fade-in" style="animation-delay: 0.1s">
    <view class="card-header">
      <view class="card-title">备份选项</view>
    </view>
    <view class="card-body">
      <!-- 备份范围选择 -->
      <view class="backup-options">
        <view class="option-group">
          <view class="option-title">备份范围</view>
          <view class="radio-group">
            <view
              class="radio-item {{backupRange === 'all' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="all"
            >
              <view class="radio-icon">{{backupRange === 'all' ? '●' : '○'}}</view>
              <view class="radio-text">
                <view class="radio-label">备份所有数据</view>
                <view class="radio-desc">包含所有账单记录和分类信息</view>
              </view>
            </view>
            <view
              class="radio-item {{backupRange === 'month' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="month"
            >
              <view class="radio-icon">{{backupRange === 'month' ? '●' : '○'}}</view>
              <view class="radio-text">
                <view class="radio-label">按月备份</view>
                <view class="radio-desc">备份指定月份的数据</view>
              </view>
            </view>
            <view
              class="radio-item {{backupRange === 'week' ? 'active' : ''}}"
              bindtap="onBackupRangeChange"
              data-range="week"
            >
              <view class="radio-icon">{{backupRange === 'week' ? '●' : '○'}}</view>
              <view class="radio-text">
                <view class="radio-label">按周备份</view>
                <view class="radio-desc">备份最近一周的数据</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 月份选择 -->
        <view wx:if="{{backupRange === 'month'}}" class="month-selector">
          <view class="selector-label">选择月份:</view>
          <picker
            mode="date"
            fields="month"
            value="{{selectedMonth}}"
            bindchange="onMonthChange"
          >
            <view class="month-picker">
              <text>{{selectedMonth}}</text>
              <text class="picker-icon">📅</text>
            </view>
          </picker>
        </view>

        <!-- 文件格式选择 -->
        <view class="option-group">
          <view class="option-title">文件格式</view>
          <view class="format-tabs">
            <view
              class="format-tab {{exportFormat === 'json' ? 'active' : ''}}"
              bindtap="onFormatChange"
              data-format="json"
            >
              JSON
            </view>
            <view
              class="format-tab {{exportFormat === 'txt' ? 'active' : ''}}"
              bindtap="onFormatChange"
              data-format="txt"
            >
              TXT
            </view>
          </view>
          <view class="format-desc">
            <text wx:if="{{exportFormat === 'json'}}">JSON格式便于数据导入和程序处理</text>
            <text wx:if="{{exportFormat === 'txt'}}">TXT格式便于人工阅读和打印</text>
          </view>
        </view>
      </view>

      <!-- 备份按钮 -->
      <view class="backup-actions">
        <button
          class="btn btn-primary backup-btn"
          style="height: fit-content;"
          bindtap="onExportData"
          disabled="{{isExporting}}"
        >
          {{isExporting ? '导出中...' : '开始备份'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 导入数据 -->
  <view class="card fade-in" style="animation-delay: 0.2s">
    <view class="card-header">
      <view class="card-title">导入数据</view>
    </view>
    <view class="card-body">
      <view class="import-section">
        <view class="import-desc">
          <text>选择之前备份的JSON文件来恢复数据</text>
        </view>
        <button
          class="btn btn-secondary import-btn"
          style="height: fit-content;"
          bindtap="onImportData"
          disabled="{{isImporting}}"
        >
          {{isImporting ? '导入中...' : '选择文件导入'}}
        </button>
        <view class="import-warning">
          <text>⚠️ 导入数据将与现有数据合并，请谨慎操作</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="card fade-in" style="animation-delay: 0.3s">
    <view class="card-header">
      <view class="card-title">💡 使用说明</view>
    </view>
    <view class="card-body">
      <view class="tips-list">
        <view class="tip-item">
          <text class="tip-number">1.</text>
          <text class="tip-text">定期备份数据，避免数据丢失</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">2.</text>
          <text class="tip-text">JSON格式可用于数据导入，TXT格式便于查看</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">3.</text>
          <text class="tip-text">导入数据前建议先备份当前数据</text>
        </view>
        <view class="tip-item">
          <text class="tip-number">4.</text>
          <text class="tip-text">备份文件保存在微信文件管理中</text>
        </view>
      </view>
    </view>
  </view>
</view>