// test/test-data.js
// 测试数据和功能验证

const storageManager = require('../utils/storage.js')
const util = require('../utils/util.js')

// 测试数据
const testBills = [
  {
    name: '早餐',
    categoryId: 1,
    amount: 15.5,
    date: '2024-01-15',
    remark: '豆浆油条'
  },
  {
    name: '午餐',
    categoryId: 1,
    amount: 25.0,
    date: '2024-01-15',
    remark: '工作餐'
  },
  {
    name: '地铁',
    categoryId: 3,
    amount: 4.0,
    date: '2024-01-15',
    remark: '上班通勤'
  },
  {
    name: '咖啡',
    categoryId: 2,
    amount: 28.0,
    date: '2024-01-16',
    remark: '星巴克拿铁'
  },
  {
    name: '电影票',
    categoryId: 5,
    amount: 45.0,
    date: '2024-01-16',
    remark: '周末娱乐'
  }
]

// 功能测试类
class FunctionTest {
  constructor() {
    this.testResults = []
  }

  // 运行所有测试
  runAllTests() {
    console.log('开始运行功能测试...')
    
    this.testStorageManager()
    this.testUtilFunctions()
    this.testDataValidation()
    this.testStatistics()
    
    this.printResults()
  }

  // 测试存储管理器
  testStorageManager() {
    console.log('测试存储管理器...')
    
    try {
      // 清空数据
      storageManager.clearAllData()
      
      // 测试添加账单
      testBills.forEach(bill => {
        const result = storageManager.addBill(bill)
        this.assert(result.success, '添加账单应该成功')
        this.assert(result.data.id, '添加的账单应该有ID')
      })
      
      // 测试获取账单
      const bills = storageManager.getBills()
      this.assert(bills.length === testBills.length, '获取的账单数量应该正确')
      
      // 测试更新账单
      const firstBill = bills[0]
      const updateResult = storageManager.updateBill(firstBill.id, { amount: 20.0 })
      this.assert(updateResult.success, '更新账单应该成功')
      this.assert(updateResult.data.amount === 20.0, '更新的金额应该正确')
      
      // 测试删除账单
      const deleteResult = storageManager.deleteBill(firstBill.id)
      this.assert(deleteResult.success, '删除账单应该成功')
      
      const remainingBills = storageManager.getBills()
      this.assert(remainingBills.length === testBills.length - 1, '删除后账单数量应该减少')
      
      this.testResults.push({ test: '存储管理器', status: 'PASS' })
    } catch (error) {
      this.testResults.push({ test: '存储管理器', status: 'FAIL', error: error.message })
    }
  }

  // 测试工具函数
  testUtilFunctions() {
    console.log('测试工具函数...')
    
    try {
      // 测试日期格式化
      const today = util.getTodayDate()
      this.assert(today.match(/^\d{4}-\d{2}-\d{2}$/), '今天日期格式应该正确')
      
      // 测试金额格式化
      const formattedAmount = util.formatAmount(123.456)
      this.assert(formattedAmount === '123.46', '金额格式化应该正确')
      
      // 测试金额验证
      this.assert(util.validateAmount('123.45'), '有效金额应该通过验证')
      this.assert(!util.validateAmount('abc'), '无效金额应该不通过验证')
      this.assert(!util.validateAmount('-10'), '负数应该不通过验证')
      
      // 测试日期验证
      this.assert(util.validateDate('2024-01-15'), '有效日期应该通过验证')
      this.assert(!util.validateDate('2024-13-01'), '无效日期应该不通过验证')
      
      // 测试日期范围计算
      const days = util.getDaysBetween('2024-01-01', '2024-01-03')
      this.assert(days === 3, '日期范围计算应该正确')
      
      this.testResults.push({ test: '工具函数', status: 'PASS' })
    } catch (error) {
      this.testResults.push({ test: '工具函数', status: 'FAIL', error: error.message })
    }
  }

  // 测试数据验证
  testDataValidation() {
    console.log('测试数据验证...')
    
    try {
      // 测试账单数据结构
      const bills = storageManager.getBills()
      bills.forEach(bill => {
        this.assert(bill.id, '账单应该有ID')
        this.assert(bill.name, '账单应该有名称')
        this.assert(typeof bill.amount === 'number', '账单金额应该是数字')
        this.assert(bill.categoryId, '账单应该有分类ID')
        this.assert(bill.date, '账单应该有日期')
        this.assert(bill.createTime, '账单应该有创建时间')
        this.assert(bill.updateTime, '账单应该有更新时间')
      })
      
      // 测试分类数据
      const categories = storageManager.getCategories()
      this.assert(categories.length > 0, '应该有预设分类')
      categories.forEach(category => {
        this.assert(category.id, '分类应该有ID')
        this.assert(category.name, '分类应该有名称')
        this.assert(category.icon, '分类应该有图标')
        this.assert(category.color, '分类应该有颜色')
      })
      
      this.testResults.push({ test: '数据验证', status: 'PASS' })
    } catch (error) {
      this.testResults.push({ test: '数据验证', status: 'FAIL', error: error.message })
    }
  }

  // 测试统计功能
  testStatistics() {
    console.log('测试统计功能...')
    
    try {
      const bills = storageManager.getBills()
      
      // 测试总金额计算
      const totalAmount = bills.reduce((sum, bill) => sum + bill.amount, 0)
      this.assert(totalAmount > 0, '总金额应该大于0')
      
      // 测试按日期筛选
      const todayBills = storageManager.getBillsByDateRange('2024-01-15', '2024-01-15')
      this.assert(Array.isArray(todayBills), '按日期筛选应该返回数组')
      
      // 测试按分类筛选
      const categoryBills = storageManager.getBillsByCategory(1)
      this.assert(Array.isArray(categoryBills), '按分类筛选应该返回数组')
      
      // 测试搜索功能
      const searchResults = storageManager.searchBills('早餐')
      this.assert(Array.isArray(searchResults), '搜索应该返回数组')
      
      this.testResults.push({ test: '统计功能', status: 'PASS' })
    } catch (error) {
      this.testResults.push({ test: '统计功能', status: 'FAIL', error: error.message })
    }
  }

  // 断言函数
  assert(condition, message) {
    if (!condition) {
      throw new Error(message)
    }
  }

  // 打印测试结果
  printResults() {
    console.log('\n=== 测试结果 ===')
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌'
      console.log(`${status} ${result.test}: ${result.status}`)
      if (result.error) {
        console.log(`   错误: ${result.error}`)
      }
    })
    
    const passCount = this.testResults.filter(r => r.status === 'PASS').length
    const totalCount = this.testResults.length
    console.log(`\n总计: ${passCount}/${totalCount} 测试通过`)
    
    if (passCount === totalCount) {
      console.log('🎉 所有测试通过！')
    } else {
      console.log('⚠️ 部分测试失败，请检查代码')
    }
  }

  // 生成测试数据
  generateTestData() {
    console.log('生成测试数据...')
    
    // 清空现有数据
    storageManager.clearAllData()
    
    // 添加测试账单
    testBills.forEach(bill => {
      storageManager.addBill(bill)
    })
    
    console.log(`已生成 ${testBills.length} 条测试数据`)
  }

  // 清理测试数据
  cleanTestData() {
    console.log('清理测试数据...')
    storageManager.clearAllData()
    console.log('测试数据已清理')
  }
}

// 导出测试类
module.exports = FunctionTest

// 使用示例：
// const test = new FunctionTest()
// test.generateTestData()  // 生成测试数据
// test.runAllTests()       // 运行所有测试
// test.cleanTestData()     // 清理测试数据
