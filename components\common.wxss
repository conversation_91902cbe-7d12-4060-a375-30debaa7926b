/* components/common.wxss */
/* 通用组件样式 */

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 摇摆动画 */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

/* 弹跳进入动画 */
.bounce-in {
  animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滑入动画 */
.slide-in-up {
  animation: slideInUp 0.4s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-down {
  animation: slideInDown 0.4s ease;
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 渐显动画 */
.fade-in-scale {
  animation: fadeInScale 0.5s ease;
}

@keyframes fadeInScale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 成功状态动画 */
.success-check {
  animation: successCheck 0.6s ease;
}

@keyframes successCheck {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 错误状态动画 */
.error-shake {
  animation: errorShake 0.5s ease;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10rpx); }
  20%, 40%, 60%, 80% { transform: translateX(10rpx); }
}

/* 按钮点击反馈 */
.btn-press {
  animation: btnPress 0.1s ease;
}

@keyframes btnPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 卡片悬浮效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}

/* 数字滚动动画 */
.number-roll {
  animation: numberRoll 0.8s ease;
}

@keyframes numberRoll {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 进度条动画 */
.progress-bar {
  position: relative;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: 4rpx;
  animation: progressFill 1s ease;
}

@keyframes progressFill {
  from { width: 0; }
  to { width: var(--progress, 0%); }
}

/* 呼吸灯效果 */
.breathing {
  animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* 旋转加载 */
.rotate-loading {
  animation: rotateLoading 2s linear infinite;
}

@keyframes rotateLoading {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 弹性缩放 */
.elastic-scale {
  animation: elasticScale 0.6s ease;
}

@keyframes elasticScale {
  0% { transform: scale(1); }
  30% { transform: scale(1.25); }
  40% { transform: scale(0.75); }
  60% { transform: scale(1.15); }
  80% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2rpx solid #4CAF50;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #4CAF50; }
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  page {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 触摸反馈 */
.touch-feedback {
  transition: all 0.1s ease;
}

.touch-feedback:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 渐变背景动画 */
.gradient-animation {
  background: linear-gradient(-45deg, #4CAF50, #45a049, #667eea, #764ba2);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
