// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化数据存储
    this.initStorage()
  },

  // 初始化本地存储
  initStorage() {
    try {
      // 检查是否已有账单数据
      const bills = wx.getStorageSync('bills')
      if (!bills) {
        wx.setStorageSync('bills', [])
      }

      // 检查是否已有分类数据
      const categories = wx.getStorageSync('categories')
      if (!categories) {
        wx.setStorageSync('categories', [
          { id: 1, name: '一日三餐', icon: '🍽️', color: '#FF6B6B' },
          { id: 2, name: '零食饮料', icon: '🍿', color: '#4ECDC4' },
          { id: 3, name: '出行交通', icon: '🚗', color: '#45B7D1' },
          { id: 4, name: '生活购物', icon: '🛒', color: '#96CEB4' },
          { id: 5, name: '爱好娱乐', icon: '🎮', color: '#FFEAA7' },
          { id: 6, name: '其他', icon: '📦', color: '#DDA0DD' }
        ])
      }
    } catch (e) {
      console.error('初始化存储失败:', e)
    }
  },

  globalData: {
    userInfo: null
  }
})
