<!--pages/list/list.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">账单列表</view>
    <view class="header-subtitle">管理你的每一笔记录 ?</view>
  </view>

  <!-- 搜索和筛选 -->
  <view class="card fade-in">
    <view class="card-body">
      <!-- 搜索框 -->
      <view class="search-box">
        <input
          class="search-input"
          placeholder="搜索账单名称或备注"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view class="search-icon" bindtap="onSearch">?</view>
      </view>

      <!-- 筛选选项 -->
      <view class="filter-section">
        <view class="filter-row">
          <view class="filter-label">日期筛选:</view>
          <picker
            mode="date"
            value="{{filterStartDate}}"
            bindchange="onStartDateChange"
          >
            <view class="date-picker-small">
              {{filterStartDate || '开始日期'}}
            </view>
          </picker>
          <text class="date-separator">至</text>
          <picker
            mode="date"
            value="{{filterEndDate}}"
            bindchange="onEndDateChange"
          >
            <view class="date-picker-small">
              {{filterEndDate || '结束日期'}}
            </view>
          </picker>
        </view>

        <view class="filter-row">
          <view class="filter-label">分类筛选:</view>
          <picker
            range="{{categoryOptions}}"
            range-key="name"
            value="{{selectedCategoryIndex}}"
            bindchange="onCategoryChange"
          >
            <view class="category-picker">
              {{selectedCategoryIndex >= 0 ? categoryOptions[selectedCategoryIndex].name : '全部分类'}}
            </view>
          </picker>
        </view>

        <view class="filter-actions">
          <button class="btn btn-secondary filter-btn" bindtap="onClearFilter">清除筛选</button>
          <button class="btn btn-primary filter-btn" bindtap="onApplyFilter">应用筛选</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 账单列表 -->
  <view class="card fade-in" style="animation-delay: 0.1s">
    <view class="card-header">
      <view class="card-title">
        账单记录
        <text class="bill-count">({{filteredBills.length}}条)</text>
      </view>
      <view class="sort-options">
        <view
          class="sort-item {{sortBy === 'date' ? 'active' : ''}}"
          bindtap="onSortChange"
          data-sort="date"
        >
          按日期
        </view>
        <view
          class="sort-item {{sortBy === 'amount' ? 'active' : ''}}"
          bindtap="onSortChange"
          data-sort="amount"
        >
          按金额
        </view>
      </view>
    </view>
    <view class="card-body">
      <view wx:if="{{filteredBills.length === 0}}" class="empty-state">
        <image src="/images/empty-list.png" class="empty-image"></image>
        <text>暂无账单记录</text>
        <button class="btn btn-primary mt-20" bindtap="goToRecord">去记账</button>
      </view>
      <view wx:else class="bill-list">
        <view
          wx:for="{{filteredBills}}"
          wx:key="id"
          class="bill-item"
          bindtap="onBillDetail"
          data-bill="{{item}}"
        >
          <view class="bill-main">
            <view class="bill-left">
              <view class="bill-category">
                <view class="category-icon-small" style="background-color: {{item.categoryColor}}">
                  {{item.categoryIcon}}
                </view>
                <view class="category-info">
                  <view class="bill-name">{{item.name}}</view>
                  <view class="bill-category-name">{{item.categoryName}}</view>
                </view>
              </view>
              <view class="bill-meta">
                <view class="bill-date">{{item.date}}</view>
                <view wx:if="{{item.remark}}" class="bill-remark">{{item.remark}}</view>
              </view>
            </view>
            <view class="bill-right">
              <view class="bill-amount">?{{item.amount}}</view>
              <view class="bill-actions">
                <view class="action-btn detail-btn" bindtap="onBillDetail" data-bill="{{item}}">详情</view>
                <view class="action-btn edit-btn" bindtap="onBillEdit" data-id="{{item.id}}">编辑</view>
                <view class="action-btn delete-btn" bindtap="onBillDelete" data-id="{{item.id}}">删除</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动备份按钮 -->
  <view class="float-btn" bindtap="goToBackup">
    备份
  </view>

  <!-- 账单详情弹窗 -->
  <view wx:if="{{showDetailModal}}" class="modal-overlay" bindtap="closeDetailModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <view class="modal-title">账单详情</view>
        <view class="modal-close" bindtap="closeDetailModal">×</view>
      </view>
      <view class="modal-body">
        <view class="detail-row">
          <view class="detail-label">账单名称:</view>
          <view class="detail-value">{{selectedBill.name}}</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">分类:</view>
          <view class="detail-value">
            <view class="category-tag" style="background-color: {{selectedBill.categoryColor}}">
              {{selectedBill.categoryIcon}} {{selectedBill.categoryName}}
            </view>
          </view>
        </view>
        <view class="detail-row">
          <view class="detail-label">金额:</view>
          <view class="detail-value amount-large">?{{selectedBill.amount}}</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">日期:</view>
          <view class="detail-value">{{selectedBill.date}}</view>
        </view>
        <view class="detail-row" wx:if="{{selectedBill.remark}}">
          <view class="detail-label">备注:</view>
          <view class="detail-value">{{selectedBill.remark}}</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">创建时间:</view>
          <view class="detail-value">{{selectedBill.createTimeText}}</view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn btn-secondary" bindtap="closeDetailModal">关闭</button>
        <button class="btn btn-primary" bindtap="editFromDetail">编辑</button>
      </view>
    </view>
  </view>
</view>