/* pages/statistics/statistics.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}


/* 时间筛选 */
.time-filter {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 20rpx;
}

.time-filter-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-filter-item.active {
  background: #667eea;
  color: white;
  font-weight: 500;
}

.custom-month {
  margin-top: 20rpx;
}

.month-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  background: #fff;
  cursor: pointer;
}

.picker-icon {
  font-size: 32rpx;
}

/* 统计概览 */
.period-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 16rpx;
  border: 1rpx solid #e0e0e0;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 图表标签页 */
.chart-tabs {
  display: flex;
  gap: 10rpx;
}

.chart-tab {
  padding: 15rpx 25rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background: #667eea;
  color: white;
}

/* 图表容器 */
.chart-container {
  margin-top: 30rpx;
  min-height: 400rpx;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

/* 日支出图表 */
.daily-chart {
  height: 400rpx;
}

.chart-wrapper {
  display: flex;
  align-items: end;
  height: 300rpx;
  padding: 20rpx 0;
  gap: 8rpx;
  overflow-x: auto;
}

.chart-bar {
  min-width: 30rpx;
  background: #667eea;
  border-radius: 4rpx 4rpx 0 0;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chart-bar:hover {
  opacity: 0.8;
}

.bar-value {
  position: absolute;
  top: -30rpx;
  font-size: 20rpx;
  color: #333;
  white-space: nowrap;
  transform: translateX(-50%);
  left: 50%;
}

.chart-labels {
  display: flex;
  gap: 8rpx;
  padding: 10rpx 0;
  overflow-x: auto;
}

.chart-label {
  min-width: 30rpx;
  text-align: center;
  font-size: 20rpx;
  color: #666;
}

/* 分类图表 */
.category-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pie-chart-container {
  margin-bottom: 30rpx;
}

.pie-canvas {
  width: 300rpx;
  height: 300rpx;
}

.category-legend {
  width: 100%;
  max-height: 300rpx;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.legend-item:last-child {
  border-bottom: none;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.legend-text {
  flex: 1;
}

.legend-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.legend-value {
  font-size: 24rpx;
  color: #666;
}

/* 详细数据 */
.detail-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-date {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.detail-amount {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 600;
}

.detail-count {
  font-size: 24rpx;
  color: #666;
}

/* 分析建议 */
.suggestions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.suggestion-item {
  padding: 20rpx;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-radius: 12rpx;
  border-left: 4rpx solid #f39c12;
  font-size: 26rpx;
  color: #856404;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-wrapper {
    gap: 4rpx;
  }
  
  .chart-bar {
    min-width: 20rpx;
  }
  
  .chart-label {
    min-width: 20rpx;
    font-size: 18rpx;
  }
}

/* 动画效果 */
.chart-bar {
  animation: barGrow 0.6s ease-out;
}

@keyframes barGrow {
  from {
    height: 0;
  }
  to {
    height: var(--bar-height, 0);
  }
}

.stat-item {
  transition: transform 0.3s ease;
}

.stat-item:active {
  transform: scale(0.98);
}

/* 滚动条样式 */
.chart-wrapper::-webkit-scrollbar,
.chart-labels::-webkit-scrollbar,
.detail-list::-webkit-scrollbar,
.category-legend::-webkit-scrollbar {
  height: 6rpx;
  width: 6rpx;
}

.chart-wrapper::-webkit-scrollbar-thumb,
.chart-labels::-webkit-scrollbar-thumb,
.detail-list::-webkit-scrollbar-thumb,
.category-legend::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3rpx;
}

.chart-wrapper::-webkit-scrollbar-track,
.chart-labels::-webkit-scrollbar-track,
.detail-list::-webkit-scrollbar-track,
.category-legend::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3rpx;
}
