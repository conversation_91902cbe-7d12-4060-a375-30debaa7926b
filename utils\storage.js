// utils/storage.js
// 数据存储管理模块

/**
 * 账单数据结构
 * {
 *   id: string,           // 唯一标识
 *   name: string,         // 账单名称
 *   categoryId: number,   // 分类ID
 *   amount: number,       // 金额
 *   date: string,         // 日期 YYYY-MM-DD
 *   remark: string,       // 备注
 *   createTime: number,   // 创建时间戳
 *   updateTime: number    // 更新时间戳
 * }
 */

class StorageManager {
  constructor() {
    this.BILLS_KEY = 'bills'
    this.CATEGORIES_KEY = 'categories'
  }

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 获取所有账单
  getBills() {
    try {
      return wx.getStorageSync(this.BILLS_KEY) || []
    } catch (e) {
      console.error('获取账单数据失败:', e)
      return []
    }
  }

  // 添加账单
  addBill(billData) {
    try {
      const bills = this.getBills()
      const newBill = {
        id: this.generateId(),
        ...billData,
        createTime: Date.now(),
        updateTime: Date.now()
      }
      bills.push(newBill)
      wx.setStorageSync(this.BILLS_KEY, bills)
      return { success: true, data: newBill }
    } catch (e) {
      console.error('添加账单失败:', e)
      return { success: false, error: e.message }
    }
  }

  // 更新账单
  updateBill(id, updateData) {
    try {
      const bills = this.getBills()
      const index = bills.findIndex(bill => bill.id === id)
      if (index === -1) {
        return { success: false, error: '账单不存在' }
      }
      bills[index] = {
        ...bills[index],
        ...updateData,
        updateTime: Date.now()
      }
      wx.setStorageSync(this.BILLS_KEY, bills)
      return { success: true, data: bills[index] }
    } catch (e) {
      console.error('更新账单失败:', e)
      return { success: false, error: e.message }
    }
  }

  // 删除账单
  deleteBill(id) {
    try {
      const bills = this.getBills()
      const filteredBills = bills.filter(bill => bill.id !== id)
      if (bills.length === filteredBills.length) {
        return { success: false, error: '账单不存在' }
      }
      wx.setStorageSync(this.BILLS_KEY, filteredBills)
      return { success: true }
    } catch (e) {
      console.error('删除账单失败:', e)
      return { success: false, error: e.message }
    }
  }

  // 根据ID获取账单
  getBillById(id) {
    try {
      const bills = this.getBills()
      return bills.find(bill => bill.id === id) || null
    } catch (e) {
      console.error('获取账单失败:', e)
      return null
    }
  }

  // 获取分类列表
  getCategories() {
    try {
      return wx.getStorageSync(this.CATEGORIES_KEY) || []
    } catch (e) {
      console.error('获取分类数据失败:', e)
      return []
    }
  }

  // 根据分类ID获取分类信息
  getCategoryById(id) {
    try {
      const categories = this.getCategories()
      return categories.find(cat => cat.id === id) || null
    } catch (e) {
      console.error('获取分类失败:', e)
      return null
    }
  }

  // 按日期范围筛选账单
  getBillsByDateRange(startDate, endDate) {
    try {
      const bills = this.getBills()
      return bills.filter(bill => {
        return bill.date >= startDate && bill.date <= endDate
      })
    } catch (e) {
      console.error('按日期筛选账单失败:', e)
      return []
    }
  }

  // 按分类筛选账单
  getBillsByCategory(categoryId) {
    try {
      const bills = this.getBills()
      return bills.filter(bill => bill.categoryId === categoryId)
    } catch (e) {
      console.error('按分类筛选账单失败:', e)
      return []
    }
  }

  // 搜索账单
  searchBills(keyword) {
    try {
      const bills = this.getBills()
      const lowerKeyword = keyword.toLowerCase()
      return bills.filter(bill => {
        return bill.name.toLowerCase().includes(lowerKeyword) ||
               bill.remark.toLowerCase().includes(lowerKeyword)
      })
    } catch (e) {
      console.error('搜索账单失败:', e)
      return []
    }
  }

  // 清空所有数据
  clearAllData() {
    try {
      wx.removeStorageSync(this.BILLS_KEY)
      wx.removeStorageSync(this.CATEGORIES_KEY)
      return { success: true }
    } catch (e) {
      console.error('清空数据失败:', e)
      return { success: false, error: e.message }
    }
  }

  // 导出数据
  exportData(format = 'json') {
    try {
      const bills = this.getBills()
      const categories = this.getCategories()
      const data = {
        bills,
        categories,
        exportTime: new Date().toISOString(),
        version: '1.0'
      }

      if (format === 'json') {
        return JSON.stringify(data, null, 2)
      } else if (format === 'txt') {
        let txtContent = '星帐数据导出\n'
        txtContent += `导出时间: ${new Date().toLocaleString()}\n\n`
        
        bills.forEach((bill, index) => {
          const category = this.getCategoryById(bill.categoryId)
          txtContent += `${index + 1}. ${bill.name}\n`
          txtContent += `   分类: ${category ? category.name : '未知'}\n`
          txtContent += `   金额: ¥${bill.amount}\n`
          txtContent += `   日期: ${bill.date}\n`
          txtContent += `   备注: ${bill.remark || '无'}\n\n`
        })
        
        return txtContent
      }
    } catch (e) {
      console.error('导出数据失败:', e)
      return null
    }
  }

  // 导入数据（不覆盖相同ID的数据）
  importData(dataString) {
    try {
      const data = JSON.parse(dataString)

      // 验证数据格式
      if (!data.bills || !Array.isArray(data.bills)) {
        return { success: false, error: '数据格式不正确' }
      }

      // 获取现有数据
      const existingBills = this.getBills()
      const existingCategories = this.getCategories()

      // 创建现有ID的集合
      const existingBillIds = new Set(existingBills.map(bill => bill.id))
      const existingCategoryIds = new Set(existingCategories.map(cat => cat.id))

      // 过滤掉已存在ID的账单
      const newBills = data.bills.filter(bill => !existingBillIds.has(bill.id))

      // 合并账单数据
      const mergedBills = [...existingBills, ...newBills]

      // 处理分类数据
      let mergedCategories = existingCategories
      if (data.categories && Array.isArray(data.categories)) {
        const newCategories = data.categories.filter(cat => !existingCategoryIds.has(cat.id))
        mergedCategories = [...existingCategories, ...newCategories]
      }

      // 保存合并后的数据
      wx.setStorageSync(this.BILLS_KEY, mergedBills)
      wx.setStorageSync(this.CATEGORIES_KEY, mergedCategories)

      const importedCount = newBills.length
      const skippedCount = data.bills.length - newBills.length

      let message = `成功导入${importedCount}条新记录`
      if (skippedCount > 0) {
        message += `，跳过${skippedCount}条重复记录`
      }

      return {
        success: true,
        message,
        imported: importedCount,
        skipped: skippedCount
      }
    } catch (e) {
      console.error('导入数据失败:', e)
      return { success: false, error: e.message }
    }
  }
}

// 创建单例实例
const storageManager = new StorageManager()

module.exports = storageManager
