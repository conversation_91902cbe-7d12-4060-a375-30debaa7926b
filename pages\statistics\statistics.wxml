<!--pages/statistics/statistics.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">统计分析</view>
    <view class="header-subtitle">了解你的消费习惯 📊</view>
    <image class="header-animation" src="/images/record-animation.gif" ></image>
  </view>

  <!-- 时间筛选 -->
  <view class="card fade-in">
    <view class="card-body">
      <view class="time-filter">
        <view 
          wx:for="{{timeFilters}}" 
          wx:key="value"
          class="time-filter-item {{currentFilter === item.value ? 'active' : ''}}"
          bindtap="onTimeFilterChange"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
      
      <!-- 自定义月份选择 -->
      <view wx:if="{{currentFilter === 'month'}}" class="custom-month">
        <picker 
          mode="date" 
          fields="month"
          value="{{selectedMonth}}" 
          bindchange="onMonthChange"
        >
          <view class="month-picker">
            <text>{{selectedMonth}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="card fade-in" style="animation-delay: 0.1s">
    <view class="card-header">
      <view class="card-title">统计概览</view>
      <view class="period-text">{{periodText}}</view>
    </view>
    <view class="card-body">
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-value">¥{{statistics.totalAmount}}</view>
          <view class="stat-label">总支出</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">¥{{statistics.avgDaily}}</view>
          <view class="stat-label">日均支出</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">¥{{statistics.maxDaily}}</view>
          <view class="stat-label">最高日支出</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">¥{{statistics.monthAmount}}</view>
          <view class="stat-label">本月支出</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 图表区域 -->
  <view class="card fade-in" style="animation-delay: 0.2s">
    <view class="card-header">
      <view class="card-title">支出趋势</view>
      <view class="chart-tabs">
        <view 
          class="chart-tab {{chartType === 'daily' ? 'active' : ''}}"
          bindtap="onChartTypeChange"
          data-type="daily"
        >
          按日
        </view>
        <view 
          class="chart-tab {{chartType === 'category' ? 'active' : ''}}"
          bindtap="onChartTypeChange"
          data-type="category"
        >
          按分类
        </view>
      </view>
    </view>
    <view class="card-body">
      <!-- 日支出趋势图 -->
      <view wx:if="{{chartType === 'daily'}}" class="chart-container">
        <view wx:if="{{dailyData.length === 0}}" class="empty-data">
          <image src="/images/empty-data.png" class="empty-image"></image>
          <text>暂无数据</text>
        </view>
        <view wx:else class="daily-chart">
          <view class="chart-wrapper">
            <view 
              wx:for="{{dailyData}}" 
              wx:key="date"
              class="chart-bar"
              style="height: {{item.height}}%; background-color: #4CAF50;"
            >
              <view class="bar-value">¥{{item.amount}}</view>
            </view>
          </view>
          <view class="chart-labels">
            <view 
              wx:for="{{dailyData}}" 
              wx:key="date"
              class="chart-label"
            >
              {{item.label}}
            </view>
          </view>
        </view>
      </view>

      <!-- 分类支出饼图 -->
      <view wx:if="{{chartType === 'category'}}" class="chart-container">
        <view wx:if="{{categoryData.length === 0}}" class="empty-data">
          <image src="/images/empty-data.png" class="empty-image"></image>
          <text>暂无数据</text>
        </view>
        <view wx:else class="category-chart">
          <view class="pie-chart-container">
            <canvas
              canvas-id="pieChart"
              class="pie-canvas"
              bindtouchstart="onCanvasTouch"
            ></canvas>
          </view>
          <view class="category-legend">
            <view 
              wx:for="{{categoryData}}" 
              wx:key="id"
              class="legend-item"
            >
              <view class="legend-color" style="background-color: {{item.color}}"></view>
              <view class="legend-text">
                <view class="legend-name">{{item.name}}</view>
                <view class="legend-value">¥{{item.amount}} ({{item.percentage}}%)</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 详细数据 -->
  <view class="card fade-in" style="animation-delay: 0.3s">
    <view class="card-header">
      <view class="card-title">详细数据</view>
    </view>
    <view class="card-body">
      <view wx:if="{{detailData.length === 0}}" class="empty-state">
        <image src="/images/empty-data.png" class="empty-image"></image>
        <text>暂无数据</text>
      </view>
      <view wx:else class="detail-list">
        <view 
          wx:for="{{detailData}}" 
          wx:key="date"
          class="detail-item"
        >
          <view class="detail-date">{{item.date}}</view>
          <view class="detail-amount">¥{{item.amount}}</view>
          <view class="detail-count">{{item.count}}笔</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分析建议 -->
  <view class="card fade-in" style="animation-delay: 0.4s" wx:if="{{suggestions.length > 0}}">
    <view class="card-header">
      <view class="card-title">💡 分析建议</view>
    </view>
    <view class="card-body">
      <view class="suggestions">
        <view 
          wx:for="{{suggestions}}" 
          wx:key="*this"
          class="suggestion-item"
        >
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>
