/* pages/list/list.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 搜索框 */
.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 10rpx 20rpx;
  margin-bottom: 30rpx;
}

.search-input {
  flex: 1;
  padding: 15rpx 0;
  font-size: 28rpx;
  background: transparent;
  border: none;
}

.search-icon {
  font-size: 32rpx;
  color: #666;
  cursor: pointer;
  padding: 10rpx;
}

/* 筛选区域 */
.filter-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
  gap: 15rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 120rpx;
}

.date-picker-small {
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  min-width: 150rpx;
  text-align: center;
}

.date-separator {
  font-size: 24rpx;
  color: #999;
  margin: 0 10rpx;
}

.category-picker {
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  min-width: 150rpx;
  text-align: center;
}

.filter-actions {
  display: flex;
  gap: 15rpx;
  margin-top: 20rpx;
}

.filter-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 26rpx;
}

/* 账单列表 */
.bill-count {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

.sort-options {
  display: flex;
  gap: 10rpx;
}

.sort-item {
  padding: 10rpx 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-item.active {
  background: #f093fb;
  color: white;
}

.bill-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.bill-item {
  background: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.bill-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.bill-main {
  display: flex;
  padding: 25rpx;
}

.bill-left {
  flex: 1;
  margin-right: 20rpx;
}

.bill-category {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.category-icon-small {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  margin-right: 15rpx;
}

.category-info {
  flex: 1;
}

.bill-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.bill-category-name {
  font-size: 24rpx;
  color: #666;
}

.bill-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.bill-date {
  font-size: 24rpx;
  color: #999;
}

.bill-remark {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bill-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.bill-amount {
  font-size: 36rpx;
  color: #f093fb;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.bill-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-btn {
  background: #e3f2fd;
  color: #1976d2;
}

.edit-btn {
  background: #fff3e0;
  color: #f57c00;
}

.delete-btn {
  background: #ffebee;
  color: #d32f2f;
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-row {
  display: flex;
  margin-bottom: 25rpx;
  align-items: flex-start;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
  margin-right: 20rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.amount-large {
  font-size: 36rpx;
  color: #f093fb;
  font-weight: 600;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-footer .btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-label {
    min-width: auto;
    margin-bottom: 10rpx;
  }
  
  .bill-main {
    flex-direction: column;
  }
  
  .bill-right {
    flex-direction: row;
    align-items: center;
    margin-top: 20rpx;
  }
  
  .bill-amount {
    margin-bottom: 0;
    margin-right: 20rpx;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
