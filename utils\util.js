// utils/util.js
// 工具函数模块

/**
 * 格式化时间
 */
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

/**
 * 格式化日期为 YYYY-MM-DD
 */
const formatDate = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${formatNumber(month)}-${formatNumber(day)}`
}

/**
 * 获取今天的日期字符串
 */
const getTodayDate = () => {
  return formatDate(new Date())
}

/**
 * 获取指定天数前的日期
 */
const getDateBefore = (days) => {
  const date = new Date()
  date.setDate(date.getDate() - days)
  return formatDate(date)
}

/**
 * 获取本月第一天
 */
const getMonthFirstDay = (year, month) => {
  return `${year}-${formatNumber(month)}-01`
}

/**
 * 获取本月最后一天
 */
const getMonthLastDay = (year, month) => {
  const date = new Date(year, month, 0)
  return formatDate(date)
}

/**
 * 数字补零
 */
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 格式化金额显示
 */
const formatAmount = (amount) => {
  return parseFloat(amount).toFixed(2)
}

/**
 * 验证金额格式
 */
const validateAmount = (amount) => {
  const regex = /^\d+(\.\d{1,2})?$/
  return regex.test(amount) && parseFloat(amount) > 0
}

/**
 * 验证日期格式
 */
const validateDate = (dateString) => {
  const regex = /^\d{4}-\d{2}-\d{2}$/
  if (!regex.test(dateString)) return false
  
  const date = new Date(dateString)
  return date instanceof Date && !isNaN(date)
}

/**
 * 计算两个日期之间的天数
 */
const getDaysBetween = (startDate, endDate) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const timeDiff = end.getTime() - start.getTime()
  return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1
}

/**
 * 获取日期范围内的所有日期
 */
const getDateRange = (startDate, endDate) => {
  const dates = []
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  while (start <= end) {
    dates.push(formatDate(new Date(start)))
    start.setDate(start.getDate() + 1)
  }
  
  return dates
}

/**
 * 防抖函数
 */
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 显示成功提示
 */
const showSuccess = (title, duration = 2000) => {
  wx.showToast({
    title,
    icon: 'success',
    duration
  })
}

/**
 * 显示错误提示
 */
const showError = (title, duration = 2000) => {
  wx.showToast({
    title,
    icon: 'error',
    duration
  })
}

/**
 * 显示加载提示
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 确认对话框
 */
const showConfirm = (content, title = '提示') => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      }
    })
  })
}

/**
 * 深拷贝对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成随机颜色
 */
const getRandomColor = () => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
    '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

module.exports = {
  formatTime,
  formatDate,
  getTodayDate,
  getDateBefore,
  getMonthFirstDay,
  getMonthLastDay,
  formatNumber,
  formatAmount,
  validateAmount,
  validateDate,
  getDaysBetween,
  getDateRange,
  debounce,
  throttle,
  showSuccess,
  showError,
  showLoading,
  hideLoading,
  showConfirm,
  deepClone,
  getRandomColor
}
