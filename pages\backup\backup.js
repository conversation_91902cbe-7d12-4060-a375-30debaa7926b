// pages/backup/backup.js
const storageManager = require('../../utils/storage.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 数据统计
    totalBills: 0,
    totalAmount: '0.00',
    dayCount: 0,
    // 备份选项
    backupRange: 'all', // all, month, week
    selectedMonth: '',
    exportFormat: 'json', // json, txt
    // 状态
    isExporting: false,
    isImporting: false,
    // 备份历史
    backupHistory: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadDataStats()
  },

  // 初始化页面
  initPage() {
    const currentMonth = new Date().toISOString().slice(0, 7)
    this.setData({
      selectedMonth: currentMonth
    })
  },

  // 加载数据统计
  loadDataStats() {
    const bills = storageManager.getBills()
    const totalAmount = bills.reduce((sum, bill) => sum + bill.amount, 0)

    // 计算记录天数
    const dates = [...new Set(bills.map(bill => bill.date))]

    this.setData({
      totalBills: bills.length,
      totalAmount: util.formatAmount(totalAmount),
      dayCount: dates.length
    })
  },

  // 备份范围选择
  onBackupRangeChange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ backupRange: range })
  },

  // 月份选择
  onMonthChange(e) {
    this.setData({ selectedMonth: e.detail.value })
  },

  // 格式选择
  onFormatChange(e) {
    const format = e.currentTarget.dataset.format
    this.setData({ exportFormat: format })
  },

  // 导出数据
  async onExportData() {
    if (this.data.isExporting) return

    this.setData({ isExporting: true })
    util.showLoading('准备导出...')

    try {
      // 获取要导出的数据
      const exportData = this.getExportData()

      if (!exportData || exportData.bills.length === 0) {
        util.showError('没有可导出的数据')
        return
      }

      // 生成文件内容
      const fileContent = storageManager.exportData(this.data.exportFormat)
      if (!fileContent) {
        util.showError('导出失败')
        return
      }

      // 生成文件名
      const fileName = this.generateFileName()

      // 保存文件
      const result = await this.saveFile(fileName, fileContent)

      if (result.success) {
        util.showSuccess('导出成功')
        // 记录备份历史
        this.recordBackupHistory(fileName, exportData.bills.length)
      } else {
        util.showError(result.error || '导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      util.showError('导出失败')
    } finally {
      util.hideLoading()
      this.setData({ isExporting: false })
    }
  },

  // 获取导出数据
  getExportData() {
    const { backupRange, selectedMonth } = this.data
    let bills = []

    switch (backupRange) {
      case 'all':
        bills = storageManager.getBills()
        break
      case 'month':
        const year = parseInt(selectedMonth.split('-')[0])
        const month = parseInt(selectedMonth.split('-')[1])
        const startDate = util.getMonthFirstDay(year, month)
        const endDate = util.getMonthLastDay(year, month)
        bills = storageManager.getBillsByDateRange(startDate, endDate)
        break
      case 'week':
        const weekStartDate = util.getDateBefore(6)
        const weekEndDate = util.getTodayDate()
        bills = storageManager.getBillsByDateRange(weekStartDate, weekEndDate)
        break
    }

    return {
      bills,
      categories: storageManager.getCategories(),
      exportTime: new Date().toISOString(),
      version: '1.0'
    }
  },

  // 生成文件名
  generateFileName() {
    const { backupRange, selectedMonth, exportFormat } = this.data
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')

    let prefix = '星帐备份'
    if (backupRange === 'month') {
      prefix += `_${selectedMonth}`
    } else if (backupRange === 'week') {
      prefix += '_最近一周'
    } else {
      prefix += '_全部数据'
    }

    return `${prefix}_${timestamp}.${exportFormat}`
  },

  // 保存文件
  saveFile(fileName, content) {
    return new Promise((resolve) => {
      const fs = wx.getFileSystemManager()
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`

      fs.writeFile({
        filePath,
        data: content,
        encoding: 'utf8',
        success: () => {
          // 保存到相册或分享
          wx.showActionSheet({
            itemList: ['保存到文件', '分享文件'],
            success: (res) => {
              if (res.tapIndex === 0) {
                this.saveToFiles(filePath, fileName)
              } else if (res.tapIndex === 1) {
                this.shareFile(filePath, fileName)
              }
            }
          })
          resolve({ success: true })
        },
        fail: (error) => {
          console.error('保存文件失败:', error)
          resolve({ success: false, error: '保存文件失败' })
        }
      })
    })
  },

  // 保存到文件管理
  saveToFiles(filePath, fileName) {
    wx.saveFileToDisk({
      filePath,
      success: () => {
        util.showSuccess('文件已保存到文件管理')
      },
      fail: () => {
        // 如果不支持saveFileToDisk，使用openDocument
        wx.openDocument({
          filePath,
          fileType: this.data.exportFormat === 'json' ? 'json' : 'txt',
          success: () => {
            util.showSuccess('文件已打开')
          },
          fail: () => {
            util.showError('打开文件失败')
          }
        })
      }
    })
  },

  // 分享文件
  shareFile(filePath, fileName) {
    wx.shareFileMessage({
      filePath,
      fileName,
      success: () => {
        util.showSuccess('分享成功')
      },
      fail: () => {
        util.showError('分享失败')
      }
    })
  },

  // 导入数据
  async onImportData() {
    if (this.data.isImporting) return

    try {
      // 选择文件
      const result = await this.chooseFile()
      if (!result.success) return

      this.setData({ isImporting: true })
      util.showLoading('导入中...')

      // 读取文件内容
      const content = await this.readFile(result.filePath)
      if (!content) {
        util.showError('读取文件失败')
        return
      }

      // 确认导入
      const confirmed = await util.showConfirm(
        '导入数据将与现有数据合并，相同ID的记录将被跳过，是否继续？',
        '导入确认'
      )
      if (!confirmed) return

      // 导入数据
      const importResult = storageManager.importData(content)
      if (importResult.success) {
        // 显示详细的导入结果
        let message = importResult.message
        if (importResult.imported === 0 && importResult.skipped > 0) {
          message = '所有记录都已存在，未导入新数据'
        }
        util.showSuccess1(message)
        this.loadDataStats()
      } else {
        util.showError(importResult.error || '导入失败')
      }
    } catch (error) {
      console.error('导入失败:', error)
      util.showError('导入失败')
    } finally {
      util.hideLoading()
      this.setData({ isImporting: false })
    }
  },

  // 选择文件
  chooseFile() {
    return new Promise((resolve) => {
      wx.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['json'],
        success: (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            resolve({
              success: true,
              filePath: res.tempFiles[0].path
            })
          } else {
            resolve({ success: false })
          }
        },
        fail: () => {
          resolve({ success: false })
        }
      })
    })
  },

  // 读取文件内容
  readFile(filePath) {
    return new Promise((resolve) => {
      const fs = wx.getFileSystemManager()
      fs.readFile({
        filePath,
        encoding: 'utf8',
        success: (res) => {
          resolve(res.data)
        },
        fail: (error) => {
          console.error('读取文件失败:', error)
          resolve(null)
        }
      })
    })
  },

  // 记录备份历史
  recordBackupHistory(fileName, recordCount) {
    try {
      const history = wx.getStorageSync('backupHistory') || []
      const newRecord = {
        id: Date.now().toString(),
        name: fileName,
        date: new Date().toLocaleString(),
        size: this.formatFileSize(fileName.length * 2), // 估算文件大小
        count: recordCount
      }

      history.unshift(newRecord)
      // 只保留最近10条记录
      if (history.length > 10) {
        history.splice(10)
      }

      wx.setStorageSync('backupHistory', history)
      this.setData({ backupHistory: history })
    } catch (error) {
      console.error('记录备份历史失败:', error)
    }
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes < 1024) return bytes + 'B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + 'KB'
    return (bytes / (1024 * 1024)).toFixed(1) + 'MB'
  },

  // 下载备份文件
  onDownloadBackup(e) {
    const item = e.currentTarget.dataset.item
    util.showSuccess('备份文件功能开发中...')
  }
})